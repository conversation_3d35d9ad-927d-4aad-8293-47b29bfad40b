import React, { useRef, useEffect, useCallback, useState } from 'react';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import './WordPressEditor.css';
import { validateImageFile, createSafeAsyncHandler } from '../../../../utils/formUtils';

interface WordPressEditorProps {
  value: string;
  onChange: (content: string) => void;
  placeholder?: string;
  height?: string;
  disabled?: boolean;
}

const WordPressEditor: React.FC<WordPressEditorProps> = ({
  value,
  onChange,
  placeholder = "Start writing...",
  height = "400px",
  disabled = false
}) => {
  const quillRef = useRef<ReactQuill>(null);
  const [isUploading, setIsUploading] = useState(false);

  // Image upload handler with proper validation
  const handleImageUpload = useCallback(() => {
    const input = document.createElement('input');
    input.setAttribute('type', 'file');
    input.setAttribute('accept', 'image/*');
    input.click();

    input.onchange = createSafeAsyncHandler(async () => {
      const file = input.files?.[0];
      if (!file) return;

      // Validate the image file
      const validation = validateImageFile(file);
      if (!validation.isValid) {
        alert(validation.error);
        return;
      }

      const quill = quillRef.current?.getEditor();
      if (!quill) return;

      const range = quill.getSelection(true);
      if (!range) return;

      setIsUploading(true);

      try {
        const formData = new FormData();
        formData.append('image', file);

        const token = localStorage.getItem('adminToken');
        if (!token) {
          alert('Please log in to upload images');
          return;
        }

        const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/resources/upload-inline-image`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`
          },
          credentials: 'include',
          mode: 'cors',
          body: formData
        });

        if (!response.ok) {
          throw new Error(`Upload failed: ${response.statusText}`);
        }

        const result = await response.json();
        
        if (result.imageUrl) {
          // Insert image at cursor position
          quill.insertEmbed(range.index, 'image', result.imageUrl);
          quill.setSelection(range.index + 1);
        } else {
          throw new Error('No image URL returned');
        }
      } catch (error) {
        console.error('Image upload failed:', error);
        alert(`Failed to upload image: ${error instanceof Error ? error.message : 'Unknown error'}`);
      } finally {
        setIsUploading(false);
      }
    }, (error) => {
      setIsUploading(false);
      console.error('Image upload failed:', error);
      alert(`Failed to upload image: ${error.message}`);
    });
  }, []);

  // Quill modules configuration
  const modules = {
    toolbar: {
      container: [
        [{ 'header': [1, 2, 3, false] }],
        ['bold', 'italic', 'underline', 'strike'],
        [{ 'color': [] }, { 'background': [] }],
        [{ 'list': 'ordered'}, { 'list': 'bullet' }],
        [{ 'indent': '-1'}, { 'indent': '+1' }],
        ['blockquote', 'code-block'],
        ['link', 'image'],
        ['clean']
      ],
      handlers: {
        image: handleImageUpload
      }
    },
    clipboard: {
      matchVisual: false
    }
  };

  const formats = [
    'header',
    'bold', 'italic', 'underline', 'strike',
    'color', 'background',
    'list', 'bullet', 'indent',
    'blockquote', 'code-block',
    'link', 'image'
  ];

  // Handle content change
  const handleChange = useCallback((content: string, delta: any, source: string) => {
    if (source === 'user') {
      onChange(content);
    }
  }, [onChange]);

  // Prevent form submission on Enter
  useEffect(() => {
    const quill = quillRef.current?.getEditor();
    if (!quill) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      // Prevent form submission on Enter in certain contexts
      if (e.key === 'Enter' && e.target instanceof HTMLElement) {
        const isInToolbar = e.target.closest('.ql-toolbar');
        if (isInToolbar) {
          e.preventDefault();
          e.stopPropagation();
        }
      }
    };

    const editorElement = quill.root;
    editorElement.addEventListener('keydown', handleKeyDown);

    return () => {
      editorElement.removeEventListener('keydown', handleKeyDown);
    };
  }, []);

  return (
    <div className="wordpress-editor">
      {isUploading && (
        <div className="upload-overlay">
          <div className="upload-spinner">Uploading image...</div>
        </div>
      )}
      <ReactQuill
        ref={quillRef}
        theme="snow"
        value={value}
        onChange={handleChange}
        modules={modules}
        formats={formats}
        placeholder={placeholder}
        readOnly={disabled}
        style={{ height }}
      />
    </div>
  );
};

export default WordPressEditor;
