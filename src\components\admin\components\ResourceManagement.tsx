import React, { useState, useEffect } from 'react';
import { apiClient } from '../../../utils/api';
import ResourceStats from './resources/ResourceStats';
import ResourceList from './resources/ResourceList';
import ResourceForm from './resources/ResourceForm';
import ResourcePreview from './resources/ResourcePreview';
import {
  ResourcePost,
  ResourceStats as ResourceStatsType,
  ResourceFormData
} from './resources/types';

const ResourceManagement: React.FC = () => {
  const [stats, setStats] = useState<ResourceStatsType | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isPreviewModalOpen, setIsPreviewModalOpen] = useState(false);
  const [selectedPost, setSelectedPost] = useState<ResourcePost | null>(null);
  const [formData, setFormData] = useState<ResourceFormData>({
    title: '',
    excerpt: '',
    content: '',
    category: 'PR Measurement',
    imageUrl: '',
    tags: '',
    author: '<PERSON> Odiakose',
    status: 'published',
    readTime: '',
    seoTitle: '',
    seoDescription: '',
    slug: ''
  });
  const [saving, setSaving] = useState(false);
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string>('');
  const [imageUploadType, setImageUploadType] = useState<'upload' | 'url'>('url');

  useEffect(() => {
    fetchResourceStats();
  }, []);

  useEffect(() => {
    // Generate slug from title
    if (formData.title) {
      const slug = formData.title
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim();
      setFormData(prev => ({ ...prev, slug }));
    }
  }, [formData.title]);

  useEffect(() => {
    // Estimate read time (average 200 words per minute)
    if (formData.content) {
      const wordCount = formData.content.split(/\s+/).length;
      const readTime = Math.ceil(wordCount / 200);
      setFormData(prev => ({ ...prev, readTime: readTime.toString() }));
    }
  }, [formData.content]);

  const fetchResourceStats = async () => {
    try {
      setLoading(true);
      const data = await apiClient.getResourcePosts();

      const totalEngagement = data.posts.reduce(
        (total: number, post: ResourcePost) =>
          total + post.engagement.likes + post.engagement.comments + post.engagement.shares,
        0
      );

      setStats({
        totalPosts: data.posts.length,
        totalEngagement,
        lastUpdated: data.meta.lastUpdated,
        posts: data.posts,
        profileName: data.profile?.name,
        profileUrl: data.profile?.linkedinUrl,
        linkedinConnected: data.meta?.linkedinConnected,
      });
    } catch (error) {
      console.error('Error fetching resource stats:', error);
      setStats({
        totalPosts: 0,
        totalEngagement: 0,
        lastUpdated: new Date().toISOString(),
        posts: [],
        profileName: 'Philip Odiakose',
        profileUrl: 'https://www.linkedin.com/in/philipodiakose/',
        linkedinConnected: false,
      });
    } finally {
      setLoading(false);
    }
  };

  const refreshResourceData = async () => {
    try {
      setRefreshing(true);

      // Refresh LinkedIn cache first
      try {
        await apiClient.request('/api/resources/refresh', {
          method: 'POST'
        });
      } catch (error) {
        console.warn('LinkedIn refresh failed, continuing with local data refresh:', error);
      }

      // Fetch updated stats
      await fetchResourceStats();

    } catch (error) {
      console.error('Error refreshing resources:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to refresh resources';
      alert(`Error: ${errorMessage}`);
    } finally {
      setRefreshing(false);
    }
  };

  const handleImageFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setImageFile(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleImageUrlChange = (url: string) => {
    setFormData(prev => ({ ...prev, imageUrl: url }));
    setImagePreview(url);
  };

  const uploadImage = async (): Promise<string> => {
    if (!imageFile) return '';

    try {
      setUploading(true);
      const formData = new FormData();
      formData.append('image', imageFile);

      const response = await apiClient.uploadResourceImage(formData);
      return response.imageUrl;
    } catch (error) {
      console.error('Error uploading image:', error);
      throw new Error('Failed to upload image');
    } finally {
      setUploading(false);
    }
  };

  const handlePreview = () => {
    setIsPreviewModalOpen(true);
  };

  const handleCreatePost = async () => {
    try {
      setSaving(true);

      // Validate required fields
      if (!formData.title.trim() || !formData.content.trim()) {
        alert('Title and content are required');
        return;
      }

      // Handle image logic properly
      let finalImageUrl = '';
      if (imageUploadType === 'url' && formData.imageUrl) {
        finalImageUrl = formData.imageUrl;
      } else if (imageUploadType === 'upload' && imageFile) {
        // Image file will be handled by the API
        finalImageUrl = ''; // Will be set by server
      } else if (imagePreview) {
        // Use existing preview (for edits)
        finalImageUrl = imagePreview;
      }

      const postData = {
        title: formData.title.trim(),
        excerpt: formData.excerpt.trim() || formData.content.substring(0, 150) + '...',
        content: formData.content.trim(),
        category: formData.category,
        imageUrl: finalImageUrl,
        tags: formData.tags,
        author: formData.author || 'Philip Odiakose',
        status: formData.status,
        readTime: formData.readTime,
        seoTitle: formData.seoTitle,
        seoDescription: formData.seoDescription,
        slug: formData.slug
      };

      if (selectedPost) {
        // For updates, pass the image file if uploading
        const imageFileToUpload = imageUploadType === 'upload' ? imageFile : undefined;
        await apiClient.updateResourcePost(selectedPost.id, postData, imageFileToUpload);
        alert('Resource updated successfully!');
      } else {
        // For new posts, pass the image file if uploading
        const imageFileToUpload = imageUploadType === 'upload' ? imageFile : undefined;
        await apiClient.createResourcePost(postData, imageFileToUpload);
        alert('Resource created successfully!');
      }

      await fetchResourceStats();
      setIsCreateModalOpen(false);
      resetForm();

    } catch (error) {
      console.error('Error saving resource post:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to save resource post';
      alert(`Error: ${errorMessage}`);
    } finally {
      setSaving(false);
    }
  };

  const handleDeletePost = async (postId: string) => {
    if (!window.confirm('Are you sure you want to delete this resource?')) {
      return;
    }

    try {
      await apiClient.deleteResourcePost(postId);
      await fetchResourceStats();
    } catch (error) {
      console.error('Error deleting resource post:', error);
      alert('Failed to delete resource post');
    }
  };

  const resetForm = () => {
    setFormData({
      title: '',
      excerpt: '',
      content: '',
      category: 'PR Measurement',
      imageUrl: '',
      tags: '',
      author: 'Philip Odiakose',
      status: 'published',
      readTime: '',
      seoTitle: '',
      seoDescription: '',
      slug: ''
    });
    setSelectedPost(null);
    setImageFile(null);
    setImagePreview('');
    setImageUploadType('url');
    setSaving(false);
    setUploading(false);

    // Reset file input
    const fileInput = document.getElementById('image-upload') as HTMLInputElement;
    if (fileInput) {
      fileInput.value = '';
    }
  };

  const openEditModal = (post: ResourcePost) => {
    setSelectedPost(post);
    setFormData({
      title: post.title,
      excerpt: post.excerpt,
      content: post.content,
      category: post.category,
      imageUrl: post.image,
      tags: post.tags?.join(', ') || '',
      author: post.author,
      status: post.status,
      readTime: post.readTime?.toString() || '',
      seoTitle: post.seoTitle || '',
      seoDescription: post.seoDescription || '',
      slug: post.slug || ''
    });
    setImagePreview(post.image);
    setIsCreateModalOpen(true);
  };

  const handleCreateNew = () => {
    setSelectedPost(null);
    setIsCreateModalOpen(true);
  };

  const handleEdit = (post: ResourcePost) => {
    setSelectedPost(post);
    setIsCreateModalOpen(true);
  };

  const handlePreview = () => {
    setIsPreviewModalOpen(true);
  };

  const handleSave = async (data: ResourceFormData, imageFile?: File) => {
    try {
      setSaving(true);

      // Validate required fields
      if (!data.title.trim() || !data.content.trim()) {
        alert('Title and content are required');
        return;
      }

      // Handle image logic properly
      let finalImageUrl = '';
      if (imageUploadType === 'url' && data.imageUrl) {
        finalImageUrl = data.imageUrl;
      } else if (imageUploadType === 'upload' && imageFile) {
        // Image file will be handled by the API
        finalImageUrl = ''; // Will be set by server
      } else if (imagePreview) {
        // Use existing preview (for edits)
        finalImageUrl = imagePreview;
      }

      const postData = {
        title: data.title.trim(),
        excerpt: data.excerpt.trim() || data.content.substring(0, 150) + '...',
        content: data.content.trim(),
        category: data.category,
        imageUrl: finalImageUrl,
        tags: data.tags,
        author: data.author || 'Philip Odiakose',
        status: data.status,
        readTime: data.readTime,
        seoTitle: data.seoTitle,
        seoDescription: data.seoDescription,
        slug: data.slug
      };

      if (selectedPost) {
        // For updates, pass the image file if uploading
        const imageFileToUpload = imageUploadType === 'upload' ? imageFile : undefined;
        await apiClient.updateResourcePost(selectedPost.id, postData, imageFileToUpload);
        alert('Resource updated successfully!');
      } else {
        // For new posts, pass the image file if uploading
        const imageFileToUpload = imageUploadType === 'upload' ? imageFile : undefined;
        await apiClient.createResourcePost(postData, imageFileToUpload);
        alert('Resource created successfully!');
      }

      await fetchResourceStats();
      setIsCreateModalOpen(false);
      setIsPreviewModalOpen(false);

    } catch (error) {
      console.error('Error saving resource post:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to save resource post';
      alert(`Error: ${errorMessage}`);
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Resource Stats Component */}
      <ResourceStats
        stats={stats}
        loading={loading}
        refreshing={refreshing}
        onRefresh={refreshResourceData}
        onCreateNew={handleCreateNew}
      />

      {/* Resource List Component */}
      <ResourceList
        posts={stats?.posts || []}
        onEdit={handleEdit}
        onDelete={handleDeletePost}
      />

      {/* Resource Form Modal */}
      <ResourceForm
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSave={handleSave}
        selectedPost={selectedPost}
        saving={saving}
        onPreview={handlePreview}
        formData={formData}
        onFormDataChange={setFormData}
        imageFile={imageFile}
        onImageFileChange={setImageFile}
        imagePreview={imagePreview}
        onImagePreviewChange={setImagePreview}
        imageUploadType={imageUploadType}
        onImageUploadTypeChange={setImageUploadType}
      />

      {/* Resource Preview Modal */}
      <ResourcePreview
        isOpen={isPreviewModalOpen}
        onClose={() => setIsPreviewModalOpen(false)}
        formData={formData}
        imagePreview={imagePreview}
        onSave={() => handleSave(formData, imageUploadType === 'upload' ? imageFile || undefined : undefined)}
        saving={saving}
        selectedPost={selectedPost}
      />
    </div>
  );
};

export default ResourceManagement;
                      </>
                    )}
                  </div>
                  <h4 className="text-lg font-medium text-gray-900 mb-2">{post.title}</h4>
                  <p className="text-gray-600 mb-3 line-clamp-2">{post.excerpt}</p>
                  <div className="flex items-center space-x-4 text-sm text-gray-500">
                    <div className="flex items-center">
                      <Heart className="h-4 w-4 mr-1" />
                      {post.engagement.likes}
                    </div>
                    <div className="flex items-center">
                      <MessageCircle className="h-4 w-4 mr-1" />
                      {post.engagement.comments}
                    </div>
                    <div className="flex items-center">
                      <Share2 className="h-4 w-4 mr-1" />
                      {post.engagement.shares}
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2 ml-4">
                  <button
                    onClick={() => window.open(`/resources/${post.id}`, '_blank')}
                    className="text-gray-600 hover:text-gray-700 p-1"
                    title="View resource"
                  >
                    <Eye className="h-4 w-4" />
                  </button>
                  {post.isManual && (
                    <>
                      <button
                        onClick={() => openEditModal(post)}
                        className="text-gray-600 hover:text-gray-700 p-1"
                        title="Edit resource"
                      >
                        <Pencil className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDeletePost(post.id)}
                       className="text-red-600 hover:text-red-700 p-1"
                        title="Delete resource"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Create/Edit Modal */}
      {isCreateModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
             <div className="flex justify-between items-center p-6 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">
                {selectedPost ? 'Edit Resource' : 'Create New Resource'}
              </h3>
              <div className="flex space-x-2">
                <button
                  onClick={handlePreview}
                  className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors inline-flex items-center"
                >
                  <Eye className="h-4 w-4 mr-2" />
                  Preview
                </button>
                <button
                  onClick={() => {
                    setIsCreateModalOpen(false);
                    resetForm();
                  }}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>
            </div>

            <div className="p-6 space-y-6">
              {/* Basic Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Title */}
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Title *
                  </label>
                  <input
                    type="text"
                    value={formData.title}
                    onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Enter resource title"
                    required
                  />
                </div>

                {/* Category */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Category *
                  </label>
                  <select
                    value={formData.category}
                    onChange={(e) => setFormData({ ...formData, category: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    required
                  >
                    {categories.map((category) => (
                      <option key={category} value={category}>
                        {category}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Status */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Status
                  </label>
                  <select
                    value={formData.status}
                    onChange={(e) => setFormData({ ...formData, status: e.target.value as 'draft' | 'published' })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="published">Published</option>
                    <option value="draft">Draft</option>
                  </select>
                </div>

                {/* Author */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Author
                  </label>
                  <input
                    type="text"
                    value={formData.author}
                    onChange={(e) => setFormData({ ...formData, author: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Author name"
                  />
                </div>

                {/* Read Time */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Read Time (minutes)
                  </label>
                  <input
                    type="number"
                    value={formData.readTime}
                    onChange={(e) => setFormData({ ...formData, readTime: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Auto-calculated"
                    min="1"
                  />
                </div>
              </div>

              {/* Excerpt */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Excerpt
                </label>
                <textarea
                  value={formData.excerpt}
                  onChange={(e) => setFormData({ ...formData, excerpt: e.target.value })}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Brief description of the resource"
                />
              </div>

              {/* Content */}
              <div>
                <div className="flex items-center justify-between mb-2">
                  <label className="block text-sm font-medium text-gray-700">
                    Content *
                  </label>
                  <EditorToolbarGuide />
                </div>
                <RichTextEditor
                  value={formData.content}
                  onChange={(content) => setFormData({ ...formData, content })}
                  placeholder="Write your resource content here... Use the toolbar above to format text, add images, links, and more."
                  height="400px"
                />
              </div>

              {/* Image Section */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Featured Image
                </label>

                {/* Image Type Toggle */}
                <div className="flex space-x-4 mb-4">
                  <button
                    type="button"
                    onClick={() => setImageUploadType('url')}
                    className={`flex items-center px-4 py-2 rounded-lg border ${
                      imageUploadType === 'url'
                        ? 'bg-blue-50 border-blue-200 text-blue-700'
                        : 'bg-gray-50 border-gray-200 text-gray-600'
                    }`}
                  >
                    <Link className="h-4 w-4 mr-2" />
                    Image URL
                  </button>
                  <button
                    type="button"
                    onClick={() => setImageUploadType('upload')}
                    className={`flex items-center px-4 py-2 rounded-lg border ${
                      imageUploadType === 'upload'
                        ? 'bg-blue-50 border-blue-200 text-blue-700'
                        : 'bg-gray-50 border-gray-200 text-gray-600'
                    }`}
                  >
                    <Upload className="h-4 w-4 mr-2" />
                    Upload File
                  </button>
                </div>

                {/* Image URL Input */}
                {imageUploadType === 'url' && (
                  <input
                    type="url"
                    value={formData.imageUrl}
                    onChange={(e) => handleImageUrlChange(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="https://example.com/image.jpg"
                  />
                )}

                {/* File Upload */}
                {imageUploadType === 'upload' && (
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleImageFileChange}
                      className="hidden"
                      id="image-upload"
                    />
                    <label htmlFor="image-upload" className="cursor-pointer">
                      <FileImage className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-sm text-gray-600">
                        Click to upload an image or drag and drop
                      </p>
                      <p className="text-xs text-gray-500 mt-1">
                        PNG, JPG, GIF up to 5MB
                      </p>
                    </label>
                  </div>
                )}

                {/* Image Preview */}
                {imagePreview && (
                  <div className="mt-4">
                    <p className="text-sm font-medium text-gray-700 mb-2">Preview:</p>
                    <img
                      src={imagePreview}
                      alt="Preview"
                      className="w-full h-48 object-cover rounded-lg border border-gray-200"
                    />
                  </div>
                )}
              </div>

              {/* Tags */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tags
                </label>
                <input
                  type="text"
                  value={formData.tags}
                  onChange={(e) => setFormData({ ...formData, tags: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="tag1, tag2, tag3"
                />
                <p className="text-xs text-gray-500 mt-1">Separate tags with commas</p>
              </div>

              {/* SEO Section */}
              <div className="border-t border-gray-200 pt-6">
                <h4 className="text-lg font-medium text-gray-900 mb-4">SEO Settings</h4>

                <div className="space-y-4">
                  {/* SEO Title */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      SEO Title
                    </label>
                    <input
                      type="text"
                      value={formData.seoTitle}
                      onChange={(e) => setFormData({ ...formData, seoTitle: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="SEO optimized title"
                      maxLength={60}
                    />
                    <p className="text-xs text-gray-500 mt-1">{formData.seoTitle.length}/60 characters</p>
                  </div>

                  {/* SEO Description */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      SEO Description
                    </label>
                    <textarea
                      value={formData.seoDescription}
                      onChange={(e) => setFormData({ ...formData, seoDescription: e.target.value })}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="SEO meta description"
                      maxLength={160}
                    />
                    <p className="text-xs text-gray-500 mt-1">{formData.seoDescription.length}/160 characters</p>
                  </div>

                  {/* Slug */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      URL Slug
                    </label>
                    <input
                      type="text"
                      value={formData.slug}
                      onChange={(e) => setFormData({ ...formData, slug: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="url-friendly-slug"
                    />
                    <p className="text-xs text-gray-500 mt-1">Auto-generated from title</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Modal Footer */}
            <div className="flex justify-end space-x-4 p-6 border-t border-gray-200">
              <button
                onClick={() => {
                  setIsCreateModalOpen(false);
                  resetForm();
                }}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleCreatePost}
                disabled={saving || uploading || !formData.title || !formData.content}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 inline-flex items-center"
              >
                {saving ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    {selectedPost ? 'Update Resource' : 'Create Resource'}
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Preview Modal */}
      {isPreviewModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl shadow-2xl w-full max-w-5xl max-h-[95vh] overflow-hidden flex flex-col">
            {/* Header */}
            <div className="flex justify-between items-center p-6 border-b border-gray-200 bg-gray-50">
              <div>
                <h3 className="text-xl font-semibold text-gray-900">Preview Resource</h3>
                <p className="text-sm text-gray-600 mt-1">See how your resource will appear to readers</p>
              </div>
              <button
                onClick={() => setIsPreviewModalOpen(false)}
                className="text-gray-400 hover:text-gray-600 transition-colors p-2 hover:bg-gray-200 rounded-lg"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            {/* Scrollable Content */}
            <div className="flex-1 overflow-y-auto">
              <div className="p-8">
                {/* Preview Content */}
                <article className="max-w-none">
                  {/* Hero Image with Fixed Dimensions */}
                  {imagePreview && (
                    <div className="relative mb-8 rounded-xl overflow-hidden shadow-lg">
                      <div className="aspect-video w-full bg-gray-100 flex items-center justify-center">
                        <img
                          src={imagePreview}
                          alt={formData.title}
                          className="w-full h-full object-cover"
                          style={{ aspectRatio: '16/9' }}
                        />
                      </div>
                    </div>
                  )}

                {/* Meta Information */}
                <div className="flex flex-wrap items-center gap-4 mb-6 text-sm text-gray-600">
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 mr-1" />
                    {new Date().toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </div>
                  <div className="flex items-center">
                    <Tag className="h-4 w-4 mr-1" />
                    {formData.category}
                  </div>
                  <div className="flex items-center">
                    <User className="h-4 w-4 mr-1" />
                    {formData.author}
                  </div>
                  {formData.readTime && (
                    <div className="flex items-center">
                      <FileText className="h-4 w-4 mr-1" />
                      {formData.readTime} min read
                    </div>
                  )}
                </div>

                {/* Title */}
                <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6 leading-tight">
                  {formData.title || 'Resource Title'}
                </h1>

                {/* Excerpt */}
                {formData.excerpt && (
                  <div className="text-xl text-gray-600 mb-8 leading-relaxed border-l-4 border-blue-500 pl-6 italic">
                    {formData.excerpt}
                  </div>
                )}

                {/* Content */}
                <div className="prose prose-lg max-w-none mb-8">
                  <div
                    className="text-gray-800 leading-relaxed"
                    dangerouslySetInnerHTML={{
                      __html: formData.content || '<p class="text-gray-500 italic">Resource content will appear here...</p>'
                    }}
                  />
                </div>

                {/* Tags */}
                {formData.tags && (
                  <div className="mb-8">
                    <h3 className="text-lg font-semibold text-gray-900 mb-3">Tags</h3>
                    <div className="flex flex-wrap gap-2">
                      {formData.tags.split(',').map((tag, index) => (
                        <span
                          key={index}
                          className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium"
                        >
                          #{tag.trim()}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {/* SEO Preview */}
                {(formData.seoTitle || formData.seoDescription) && (
                  <div className="border-t border-gray-200 pt-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-3">SEO Preview</h3>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="text-blue-600 text-lg font-medium mb-1">
                        {formData.seoTitle || formData.title}
                      </div>
                      <div className="text-green-600 text-sm mb-2">
                        https://thescienceofpublicrelations.com/resources/{formData.slug || 'resource-slug'}
                      </div>
                      <div className="text-gray-600 text-sm">
                        {formData.seoDescription || formData.excerpt || 'SEO description will appear here...'}
                      </div>
                    </div>
                  </div>
                )}
              </article>
            </div>

            {/* Preview Footer */}
            <div className="flex justify-end space-x-4 p-6 border-t border-gray-200">
              <button
                onClick={() => setIsPreviewModalOpen(false)}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                Close Preview
              </button>
              <button
                onClick={() => {
                  setIsPreviewModalOpen(false);
                  handleCreatePost();
                }}
                disabled={saving || uploading || !formData.title || !formData.content}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 inline-flex items-center"
              >
                <Save className="h-4 w-4 mr-2" />
                {selectedPost ? 'Update Resource' : 'Publish Resource'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ResourceManagement;
