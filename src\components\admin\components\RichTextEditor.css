/* Rich Text Editor Styles */
.rich-text-editor {
  margin-bottom: 1rem;
}

.rich-text-editor .ql-editor {
  min-height: 300px;
  font-size: 16px;
  line-height: 1.6;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
}

/* Toolbar styling */
.rich-text-editor .ql-toolbar {
  border: 1px solid #e2e8f0;
  border-bottom: none;
  background: #f8fafc;
  border-radius: 8px 8px 0 0;
  padding: 12px;
}

.rich-text-editor .ql-container {
  border: 1px solid #e2e8f0;
  border-radius: 0 0 8px 8px;
  font-size: 16px;
}

/* Toolbar button styling */
.rich-text-editor .ql-toolbar .ql-picker-label {
  border: 1px solid transparent;
  border-radius: 6px;
  padding: 8px 12px;
  transition: all 0.2s ease;
  font-size: 14px;
  min-height: 36px;
  display: flex;
  align-items: center;
  position: relative;
}

.rich-text-editor .ql-toolbar .ql-picker-label:hover {
  background: #e2e8f0;
  border-color: #cbd5e0;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.rich-text-editor .ql-toolbar button {
  border: 1px solid transparent;
  border-radius: 6px;
  padding: 8px;
  margin: 2px;
  transition: all 0.2s ease;
  min-width: 36px;
  min-height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.rich-text-editor .ql-toolbar button:hover {
  background: #e2e8f0;
  border-color: #cbd5e0;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.rich-text-editor .ql-toolbar button.ql-active {
  background: #3b82f6;
  color: white;
  border-color: #2563eb;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

/* Larger icons */
.rich-text-editor .ql-toolbar button svg,
.rich-text-editor .ql-toolbar .ql-picker-label svg {
  width: 18px !important;
  height: 18px !important;
}

/* Enhanced Tooltip styles */
.rich-text-editor .ql-toolbar button[title]:hover::after,
.rich-text-editor .ql-toolbar .ql-picker-label[title]:hover::after {
  content: attr(title);
  position: absolute;
  bottom: calc(100% + 8px);
  left: 50%;
  transform: translateX(-50%);
  background: #1f2937;
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
  z-index: 1000;
  pointer-events: none;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  animation: tooltipFadeIn 0.2s ease-out;
}

.rich-text-editor .ql-toolbar button[title]:hover::before,
.rich-text-editor .ql-toolbar .ql-picker-label[title]:hover::before {
  content: '';
  position: absolute;
  bottom: calc(100% + 2px);
  left: 50%;
  transform: translateX(-50%);
  border: 4px solid transparent;
  border-top-color: #1f2937;
  z-index: 1001;
  pointer-events: none;
  animation: tooltipFadeIn 0.2s ease-out;
}

@keyframes tooltipFadeIn {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(4px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* Custom button styles */
.rich-text-editor .ql-toolbar .ql-bold {
  font-weight: bold;
}

.rich-text-editor .ql-toolbar .ql-italic {
  font-style: italic;
}

.rich-text-editor .ql-toolbar .ql-underline {
  text-decoration: underline;
}

/* Editor content styling */
.rich-text-editor .ql-editor h1 {
  font-size: 2em;
  font-weight: bold;
  margin: 1em 0 0.5em 0;
  line-height: 1.2;
}

.rich-text-editor .ql-editor h2 {
  font-size: 1.5em;
  font-weight: bold;
  margin: 1em 0 0.5em 0;
  line-height: 1.3;
}

.rich-text-editor .ql-editor h3 {
  font-size: 1.25em;
  font-weight: bold;
  margin: 1em 0 0.5em 0;
  line-height: 1.4;
}

.rich-text-editor .ql-editor p {
  margin: 0 0 1em 0;
  line-height: 1.6;
}

.rich-text-editor .ql-editor blockquote {
  border-left: 4px solid #3b82f6;
  padding-left: 1em;
  margin: 1em 0;
  font-style: italic;
  background: #f8fafc;
  padding: 1em;
  border-radius: 0 8px 8px 0;
}

.rich-text-editor .ql-editor ul, 
.rich-text-editor .ql-editor ol {
  margin: 1em 0;
  padding-left: 2em;
}

.rich-text-editor .ql-editor li {
  margin: 0.5em 0;
  line-height: 1.6;
}

.rich-text-editor .ql-editor img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  margin: 1em 0;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.rich-text-editor .ql-editor a {
  color: #3b82f6;
  text-decoration: underline;
}

.rich-text-editor .ql-editor a:hover {
  color: #2563eb;
}

.rich-text-editor .ql-editor code {
  background: #f1f5f9;
  padding: 2px 4px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9em;
}

.rich-text-editor .ql-editor pre {
  background: #1e293b;
  color: #f1f5f9;
  padding: 1em;
  border-radius: 8px;
  overflow-x: auto;
  margin: 1em 0;
}

.rich-text-editor .ql-editor pre code {
  background: transparent;
  color: inherit;
  padding: 0;
}

/* Video embed styling */
.rich-text-editor .ql-editor .ql-video {
  width: 100%;
  height: 315px;
  border-radius: 8px;
  margin: 1em 0;
}

/* Checklist styling */
.rich-text-editor .ql-editor .ql-list.ql-list-check {
  list-style: none;
  padding-left: 0;
}

.rich-text-editor .ql-editor .ql-list.ql-list-check > li {
  position: relative;
  padding-left: 2em;
  margin: 0.5em 0;
}

.rich-text-editor .ql-editor .ql-list.ql-list-check > li::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0.2em;
  width: 1em;
  height: 1em;
  border: 2px solid #3b82f6;
  border-radius: 3px;
  background: white;
}

.rich-text-editor .ql-editor .ql-list.ql-list-check > li.ql-checked::before {
  background: #3b82f6;
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='m13.854 3.646-7.5 7.5a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6 10.293l7.146-7.147a.5.5 0 0 1 .708.708z'/%3e%3c/svg%3e");
  background-size: 0.8em;
  background-position: center;
  background-repeat: no-repeat;
}

.rich-text-editor .ql-editor .ql-list.ql-list-check > li.ql-checked {
  text-decoration: line-through;
  color: #6b7280;
}

/* Table styling */
.rich-text-editor .ql-editor table {
  border-collapse: collapse;
  width: 100%;
  margin: 1em 0;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
}

.rich-text-editor .ql-editor table td,
.rich-text-editor .ql-editor table th {
  border: 1px solid #e2e8f0;
  padding: 0.75em;
  text-align: left;
}

.rich-text-editor .ql-editor table th {
  background: #f8fafc;
  font-weight: 600;
  color: #374151;
}

.rich-text-editor .ql-editor table tr:nth-child(even) {
  background: #f9fafb;
}

.rich-text-editor .ql-editor table tr:hover {
  background: #f3f4f6;
}

/* Superscript and subscript */
.rich-text-editor .ql-editor .ql-script-super {
  vertical-align: super;
  font-size: 0.75em;
}

.rich-text-editor .ql-editor .ql-script-sub {
  vertical-align: sub;
  font-size: 0.75em;
}

/* Text alignment */
.rich-text-editor .ql-editor .ql-align-center {
  text-align: center;
}

.rich-text-editor .ql-editor .ql-align-right {
  text-align: right;
}

.rich-text-editor .ql-editor .ql-align-justify {
  text-align: justify;
}

/* Indentation */
.rich-text-editor .ql-editor .ql-indent-1 {
  padding-left: 3em;
}

.rich-text-editor .ql-editor .ql-indent-2 {
  padding-left: 6em;
}

.rich-text-editor .ql-editor .ql-indent-3 {
  padding-left: 9em;
}

.rich-text-editor .ql-editor .ql-indent-4 {
  padding-left: 12em;
}

.rich-text-editor .ql-editor .ql-indent-5 {
  padding-left: 15em;
}

.rich-text-editor .ql-editor .ql-indent-6 {
  padding-left: 18em;
}

.rich-text-editor .ql-editor .ql-indent-7 {
  padding-left: 21em;
}

.rich-text-editor .ql-editor .ql-indent-8 {
  padding-left: 24em;
}

/* Placeholder styling */
.rich-text-editor .ql-editor.ql-blank::before {
  color: #9ca3af;
  font-style: italic;
}

/* Focus styling */
.rich-text-editor .ql-container.ql-snow {
  border-color: #e2e8f0;
}

.rich-text-editor .ql-editor:focus {
  outline: none;
}

.rich-text-editor .ql-container:focus-within {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .rich-text-editor .ql-toolbar {
    padding: 8px;
  }
  
  .rich-text-editor .ql-toolbar .ql-formats {
    margin-right: 8px;
  }
  
  .rich-text-editor .ql-editor {
    font-size: 14px;
    min-height: 250px;
  }
}

/* Custom dropdown styling */
.rich-text-editor .ql-picker-options {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  padding: 8px 0;
}

.rich-text-editor .ql-picker-item {
  padding: 8px 16px;
  transition: background 0.2s ease;
}

.rich-text-editor .ql-picker-item:hover {
  background: #f1f5f9;
}

/* Color picker styling */
.rich-text-editor .ql-color-picker .ql-picker-options {
  width: 200px;
  padding: 8px;
}

.rich-text-editor .ql-color-picker .ql-picker-item {
  width: 20px;
  height: 20px;
  margin: 2px;
  border-radius: 4px;
  border: 1px solid #e2e8f0;
}
