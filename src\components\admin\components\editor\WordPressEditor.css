.wordpress-editor {
  position: relative;
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
}

.wordpress-editor .ql-toolbar {
  border: none;
  border-bottom: 1px solid #ddd;
  background: #f8f9fa;
  padding: 12px;
}

.wordpress-editor .ql-toolbar .ql-formats {
  margin-right: 15px;
}

.wordpress-editor .ql-toolbar button {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  margin: 0 2px;
  transition: all 0.2s ease;
}

.wordpress-editor .ql-toolbar button:hover {
  background: #e9ecef;
}

.wordpress-editor .ql-toolbar button.ql-active {
  background: #007cba;
  color: white;
}

.wordpress-editor .ql-toolbar .ql-picker {
  color: #333;
}

.wordpress-editor .ql-container {
  border: none;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  font-size: 16px;
  line-height: 1.6;
}

.wordpress-editor .ql-editor {
  padding: 20px;
  min-height: 300px;
  color: #333;
}

.wordpress-editor .ql-editor.ql-blank::before {
  color: #999;
  font-style: italic;
}

.wordpress-editor .ql-editor h1,
.wordpress-editor .ql-editor h2,
.wordpress-editor .ql-editor h3 {
  margin: 1.5em 0 0.5em 0;
  font-weight: 600;
  line-height: 1.3;
}

.wordpress-editor .ql-editor h1 {
  font-size: 2em;
}

.wordpress-editor .ql-editor h2 {
  font-size: 1.5em;
}

.wordpress-editor .ql-editor h3 {
  font-size: 1.25em;
}

.wordpress-editor .ql-editor p {
  margin: 1em 0;
}

.wordpress-editor .ql-editor img {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
  margin: 1em 0;
  display: block;
}

.wordpress-editor .ql-editor blockquote {
  border-left: 4px solid #007cba;
  margin: 1.5em 0;
  padding: 0.5em 1em;
  background: #f8f9fa;
  font-style: italic;
}

.wordpress-editor .ql-editor ul,
.wordpress-editor .ql-editor ol {
  margin: 1em 0;
  padding-left: 2em;
}

.wordpress-editor .ql-editor li {
  margin: 0.5em 0;
}

.wordpress-editor .ql-editor a {
  color: #007cba;
  text-decoration: underline;
}

.wordpress-editor .ql-editor a:hover {
  color: #005a87;
}

.wordpress-editor .ql-editor code {
  background: #f1f1f1;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: Monaco, Consolas, monospace;
  font-size: 0.9em;
}

.wordpress-editor .ql-editor pre {
  background: #f8f8f8;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 1em;
  overflow-x: auto;
  margin: 1em 0;
}

.upload-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.upload-spinner {
  background: #007cba;
  color: white;
  padding: 12px 24px;
  border-radius: 4px;
  font-weight: 500;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .wordpress-editor .ql-toolbar {
    padding: 8px;
  }
  
  .wordpress-editor .ql-toolbar .ql-formats {
    margin-right: 8px;
  }
  
  .wordpress-editor .ql-toolbar button {
    width: 28px;
    height: 28px;
    margin: 0 1px;
  }
  
  .wordpress-editor .ql-editor {
    padding: 16px;
    font-size: 14px;
  }
}
