import React, { useState, useEffect, useCallback } from 'react';
import { Plus, RefreshCw, Pen<PERSON>l, Trash2, <PERSON>, Co<PERSON>, CheckCircle } from 'lucide-react';
import ResourcePostForm from './forms/ResourcePostForm';
import { apiClient } from '../../../utils/api';

interface ResourcePost {
  id: string;
  title: string;
  excerpt: string;
  content: string;
  category: string;
  image: string;
  tags: string[];
  author: string;
  status: 'draft' | 'published';
  date: string;
  readTime: number;
  engagement: {
    likes: number;
    comments: number;
    shares: number;
  };
  seoTitle?: string;
  seoDescription?: string;
  slug?: string;
}

interface ResourceStats {
  totalPosts: number;
  totalEngagement: number;
  lastUpdated: string;
  posts: ResourcePost[];
}

const OptimizedResourceManagement: React.FC = () => {
  const [stats, setStats] = useState<ResourceStats>({
    totalPosts: 0,
    totalEngagement: 0,
    lastUpdated: '',
    posts: []
  });
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [saving, setSaving] = useState(false);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [selectedPost, setSelectedPost] = useState<ResourcePost | null>(null);
  const [copiedId, setCopiedId] = useState<string | null>(null);

  // Fetch resources from API
  const fetchResources = useCallback(async () => {
    try {
      setLoading(true);
      const response = await apiClient.getResourcePosts() as any;
      
      if (response?.posts) {
        const totalEngagement = response.posts.reduce(
          (total: number, post: ResourcePost) =>
            total + post.engagement.likes + post.engagement.comments + post.engagement.shares,
          0
        );

        setStats({
          totalPosts: response.posts.length,
          totalEngagement,
          lastUpdated: response.meta?.lastUpdated || new Date().toISOString(),
          posts: response.posts
        });
      }
    } catch (error) {
      console.error('Failed to fetch resources:', error);
      alert('Failed to load resources. Please try again.');
    } finally {
      setLoading(false);
    }
  }, []);

  // Refresh data
  const handleRefresh = useCallback(async () => {
    try {
      setRefreshing(true);
      await fetchResources();
    } finally {
      setRefreshing(false);
    }
  }, [fetchResources]);

  // Initialize component
  useEffect(() => {
    fetchResources();
  }, [fetchResources]);

  // Handle create new post
  const handleCreateNew = useCallback(() => {
    setSelectedPost(null);
    setIsFormOpen(true);
  }, []);

  // Handle edit post
  const handleEdit = useCallback((post: ResourcePost) => {
    setSelectedPost(post);
    setIsFormOpen(true);
  }, []);

  // Handle delete post
  const handleDelete = useCallback(async (postId: string) => {
    if (!confirm('Are you sure you want to delete this resource?')) {
      return;
    }

    try {
      await apiClient.deleteResourcePost(postId);
      await fetchResources();
      alert('Resource deleted successfully!');
    } catch (error) {
      console.error('Failed to delete resource:', error);
      alert('Failed to delete resource. Please try again.');
    }
  }, [fetchResources]);

  // Handle save post
  const handleSave = useCallback(async (formData: any) => {
    try {
      setSaving(true);

      const postData = {
        title: formData.title,
        excerpt: formData.excerpt || formData.content.substring(0, 150) + '...',
        content: formData.content,
        category: formData.category,
        imageUrl: formData.image,
        tags: formData.tags,
        author: formData.author,
        status: formData.status,
        seoTitle: formData.seoTitle,
        seoDescription: formData.seoDescription,
        slug: formData.slug
      };

      if (selectedPost) {
        await apiClient.updateResourcePost(selectedPost.id, postData);
        alert('Resource updated successfully!');
      } else {
        await apiClient.createResourcePost(postData);
        alert('Resource created successfully!');
      }

      setIsFormOpen(false);
      setSelectedPost(null);
      await fetchResources();
    } catch (error) {
      console.error('Failed to save resource:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to save resource';
      
      if (errorMessage.includes('Authentication') || errorMessage.includes('403')) {
        alert('Authentication failed. Please refresh the page and log in again.');
      } else {
        alert(`Error: ${errorMessage}`);
      }
      throw error;
    } finally {
      setSaving(false);
    }
  }, [selectedPost, fetchResources]);

  // Handle copy URL
  const handleCopyUrl = useCallback((post: ResourcePost) => {
    const url = `${window.location.origin}/resources/${post.slug || post.id}`;
    navigator.clipboard.writeText(url).then(() => {
      setCopiedId(post.id);
      setTimeout(() => setCopiedId(null), 2000);
    });
  }, []);

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Resource Management</h2>
            <p className="text-gray-600 mt-1">Create and manage PR resources and insights</p>
          </div>
          <button
            onClick={handleCreateNew}
            className="mt-4 sm:mt-0 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors inline-flex items-center"
          >
            <Plus className="h-4 w-4 mr-2" />
            Create Resource
          </button>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mt-6">
          <div className="bg-blue-50 rounded-lg p-4">
            <div className="text-2xl font-bold text-blue-600">{stats.totalPosts}</div>
            <div className="text-sm text-blue-600">Total Resources</div>
          </div>
          <div className="bg-green-50 rounded-lg p-4">
            <div className="text-2xl font-bold text-green-600">{stats.totalEngagement}</div>
            <div className="text-sm text-green-600">Total Engagement</div>
          </div>
          <div className="bg-purple-50 rounded-lg p-4">
            <div className="text-2xl font-bold text-purple-600">
              {stats.posts.filter(p => p.status === 'published').length}
            </div>
            <div className="text-sm text-purple-600">Published</div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-between items-center mt-6">
          <div className="text-sm text-gray-500">
            Last updated: {stats.lastUpdated ? formatDate(stats.lastUpdated) : 'Never'}
          </div>
          <button
            onClick={handleRefresh}
            disabled={refreshing}
            className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors inline-flex items-center disabled:opacity-50"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </button>
        </div>
      </div>

      {/* Resources List */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">All Resources</h3>
        </div>

        {loading ? (
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="text-gray-500 mt-2">Loading resources...</p>
          </div>
        ) : stats.posts.length === 0 ? (
          <div className="p-8 text-center">
            <p className="text-gray-500">No resources found. Create your first resource!</p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {stats.posts.map((post) => (
              <div key={post.id} className="p-6 hover:bg-gray-50 transition-colors">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h4 className="text-lg font-medium text-gray-900">{post.title}</h4>
                      <span className={`px-2 py-1 text-xs rounded-full ${
                        post.status === 'published' 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {post.status}
                      </span>
                    </div>
                    <p className="text-gray-600 text-sm mb-3">{post.excerpt}</p>
                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      <span>{post.category}</span>
                      <span>•</span>
                      <span>{formatDate(post.date)}</span>
                      <span>•</span>
                      <span>{post.readTime} min read</span>
                      <span>•</span>
                      <span>{post.engagement.likes + post.engagement.comments + post.engagement.shares} engagements</span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2 ml-4">
                    <button
                      onClick={() => handleCopyUrl(post)}
                      className="p-2 text-gray-400 hover:text-blue-600 transition-colors rounded"
                      title="Copy URL"
                    >
                      {copiedId === post.id ? (
                        <CheckCircle className="h-4 w-4 text-green-600" />
                      ) : (
                        <Copy className="h-4 w-4" />
                      )}
                    </button>
                    <button
                      onClick={() => handleEdit(post)}
                      className="p-2 text-gray-400 hover:text-blue-600 transition-colors rounded"
                      title="Edit resource"
                    >
                      <Pencil className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => handleDelete(post.id)}
                      className="p-2 text-gray-400 hover:text-red-600 transition-colors rounded"
                      title="Delete resource"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Resource Form Modal */}
      <ResourcePostForm
        isOpen={isFormOpen}
        onClose={() => {
          setIsFormOpen(false);
          setSelectedPost(null);
        }}
        onSave={handleSave}
        post={selectedPost}
        saving={saving}
      />
    </div>
  );
};

export default OptimizedResourceManagement;
