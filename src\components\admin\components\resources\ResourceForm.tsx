import React, { useState } from 'react';
import { X, Eye, Save, RefreshCw } from 'lucide-react';
import { ResourceFormProps, ResourceFormData, RESOURCE_CATEGORIES } from './types';
import ResourceImageUpload from './ResourceImageUpload';
import RichTextEditor from '../RichTextEditor';
import EditorToolbarGuide from '../EditorToolbarGuide';

interface ResourceFormExtendedProps extends ResourceFormProps {
  formData: ResourceFormData;
  onFormDataChange: (data: ResourceFormData) => void;
  imageFile: File | null;
  onImageFileChange: (file: File | null) => void;
  imagePreview: string;
  onImagePreviewChange: (preview: string) => void;
  imageUploadType: 'upload' | 'url';
  onImageUploadTypeChange: (type: 'upload' | 'url') => void;
}

const ResourceForm: React.FC<ResourceFormExtendedProps> = ({
  isOpen,
  onClose,
  onSave,
  selectedPost,
  saving,
  onPreview,
  formData,
  onFormDataChange,
  imageFile,
  onImageFileChange,
  imagePreview,
  onImagePreviewChange,
  imageUploadType,
  onImageUploadTypeChange
}) => {
  const [uploading, setUploading] = useState(false);

  const handleInputChange = (field: keyof ResourceFormData, value: string) => {
    onFormDataChange({ ...formData, [field]: value });
  };

  const handleImageFileChange = (file: File | null) => {
    onImageFileChange(file);
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        onImagePreviewChange(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    } else {
      onImagePreviewChange('');
    }
  };

  const handleImageUrlChange = (url: string) => {
    onFormDataChange({ ...formData, imageUrl: url });
    onImagePreviewChange(url);
  };

  const handleSubmit = async () => {
    if (!formData.title.trim() || !formData.content.trim()) {
      alert('Title and content are required');
      return;
    }

    await onSave(formData, imageUploadType === 'upload' ? imageFile || undefined : undefined);
  };

  const handleClose = () => {
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center p-6 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">
            {selectedPost ? 'Edit Resource' : 'Create New Resource'}
          </h3>
          <div className="flex space-x-2">
            {onPreview && (
              <button
                onClick={onPreview}
                className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors inline-flex items-center"
              >
                <Eye className="h-4 w-4 mr-2" />
                Preview
              </button>
            )}
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="h-6 w-6" />
            </button>
          </div>
        </div>

        <div className="p-6 space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Title *
              </label>
              <input
                type="text"
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter resource title..."
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Category
              </label>
              <select
                value={formData.category}
                onChange={(e) => handleInputChange('category', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                {RESOURCE_CATEGORIES.map(category => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Excerpt */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Excerpt
            </label>
            <textarea
              value={formData.excerpt}
              onChange={(e) => handleInputChange('excerpt', e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Brief description of the resource..."
            />
          </div>

          {/* Image Upload */}
          <ResourceImageUpload
            imageUploadType={imageUploadType}
            onTypeChange={onImageUploadTypeChange}
            imageFile={imageFile}
            onFileChange={handleImageFileChange}
            imagePreview={imagePreview}
            onUrlChange={handleImageUrlChange}
            imageUrl={formData.imageUrl}
            uploading={uploading}
          />

          {/* Content Editor */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Content *
            </label>
            <EditorToolbarGuide />
            <RichTextEditor
              value={formData.content}
              onChange={(value) => handleInputChange('content', value)}
              placeholder="Write your resource content here..."
            />
          </div>

          {/* Additional Fields */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tags
              </label>
              <input
                type="text"
                value={formData.tags}
                onChange={(e) => handleInputChange('tags', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="tag1, tag2, tag3..."
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Author
              </label>
              <input
                type="text"
                value={formData.author}
                onChange={(e) => handleInputChange('author', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>

          {/* Status and Read Time */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Status
              </label>
              <select
                value={formData.status}
                onChange={(e) => handleInputChange('status', e.target.value as 'draft' | 'published')}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="published">Published</option>
                <option value="draft">Draft</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Read Time (minutes)
              </label>
              <input
                type="number"
                value={formData.readTime}
                onChange={(e) => handleInputChange('readTime', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="5"
              />
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end space-x-4 p-6 border-t border-gray-200">
          <button
            onClick={handleClose}
            className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            disabled={saving || uploading || !formData.title || !formData.content}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 inline-flex items-center"
          >
            {saving ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                {selectedPost ? 'Update Resource' : 'Create Resource'}
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ResourceForm;
