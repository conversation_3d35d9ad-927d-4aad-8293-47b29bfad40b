import React, { useState, useEffect, useCallback } from 'react';
import { X, Save, Eye, Upload, Link, AlertCircle, CheckCircle } from 'lucide-react';
import WordPressEditor from '../editor/WordPressEditor';
import { ResourcePost } from '../resources/types';
import {
  createSafeSubmitHandler,
  createSafeClickHandler,
  generateSlug,
  validateRequiredFields,
  setupBeforeUnloadProtection,
  confirmAction,
  validateImageFile
} from '../../../../utils/formUtils';

interface ResourcePostFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (data: ResourceFormData) => Promise<void>;
  onPreview?: () => void;
  post?: ResourcePost | null;
  saving?: boolean;
}

interface ResourceFormData {
  title: string;
  excerpt: string;
  content: string;
  category: string;
  image: string;
  tags: string;
  author: string;
  status: 'draft' | 'published';
  seoTitle: string;
  seoDescription: string;
  slug: string;
}

const CATEGORIES = [
  'PR Measurement',
  'Digital PR', 
  'Strategy',
  'Industry Trends',
  'Case Studies',
  'Analytics',
  'Insights'
];

const ResourcePostForm: React.FC<ResourcePostFormProps> = ({
  isOpen,
  onClose,
  onSave,
  onPreview,
  post,
  saving = false
}) => {
  const [formData, setFormData] = useState<ResourceFormData>({
    title: '',
    excerpt: '',
    content: '',
    category: 'Insights',
    image: '',
    tags: '',
    author: 'Philip Odiakose',
    status: 'published',
    seoTitle: '',
    seoDescription: '',
    slug: ''
  });

  const [isDirty, setIsDirty] = useState(false);
  const [imageUploadType, setImageUploadType] = useState<'url' | 'upload'>('url');
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string>('');

  // Initialize form data when post changes
  useEffect(() => {
    if (post) {
      setFormData({
        title: post.title || '',
        excerpt: post.excerpt || '',
        content: post.content || '',
        category: post.category || 'Insights',
        image: post.image || '',
        tags: post.tags?.join(', ') || '',
        author: post.author || 'Philip Odiakose',
        status: post.status || 'published',
        seoTitle: post.seoTitle || '',
        seoDescription: post.seoDescription || '',
        slug: post.slug || ''
      });
      setImagePreview(post.image || '');
    } else {
      // Reset form for new post
      setFormData({
        title: '',
        excerpt: '',
        content: '',
        category: 'Insights',
        image: '',
        tags: '',
        author: 'Philip Odiakose',
        status: 'published',
        seoTitle: '',
        seoDescription: '',
        slug: ''
      });
      setImagePreview('');
      setImageFile(null);
    }
    setIsDirty(false);
  }, [post, isOpen]);

  // Use the utility function for slug generation

  // Handle form field changes
  const handleFieldChange = useCallback((field: keyof ResourceFormData, value: string) => {
    setFormData(prev => {
      const updated = { ...prev, [field]: value };
      
      // Auto-generate slug from title
      if (field === 'title' && (!prev.slug || prev.slug === generateSlug(prev.title))) {
        updated.slug = generateSlug(value);
      }
      
      // Auto-generate SEO title from title
      if (field === 'title' && !prev.seoTitle) {
        updated.seoTitle = value;
      }
      
      return updated;
    });
    setIsDirty(true);
  }, []);

  // Handle image upload with validation
  const handleImageUpload = useCallback((file: File) => {
    const validation = validateImageFile(file);
    if (!validation.isValid) {
      alert(validation.error);
      return;
    }

    setImageFile(file);
    const reader = new FileReader();
    reader.onload = (e) => {
      const result = e.target?.result as string;
      setImagePreview(result);
      setFormData(prev => ({ ...prev, image: result }));
      setIsDirty(true);
    };
    reader.readAsDataURL(file);
  }, []);

  // Handle form submission with validation
  const handleSubmit = useCallback(
    createSafeSubmitHandler(async () => {
      const validation = validateRequiredFields(formData, ['title', 'content']);
      if (!validation.isValid) {
        alert(`Please fill in the following required fields: ${validation.missingFields.join(', ')}`);
        return;
      }

      await onSave(formData);
      setIsDirty(false);
    }),
    [formData, onSave]
  );

  // Handle close with unsaved changes check
  const handleClose = useCallback(
    createSafeClickHandler(() => {
      if (isDirty && (formData.title.trim() || formData.content.trim())) {
        if (!confirmAction('You have unsaved changes. Are you sure you want to close?', 'Unsaved Changes')) {
          return;
        }
      }
      onClose();
    }),
    [isDirty, formData.title, formData.content, onClose]
  );

  // Prevent accidental page refresh using utility
  useEffect(() => {
    if (isOpen) {
      return setupBeforeUnloadProtection(isDirty, 'You have unsaved changes.');
    }
  }, [isDirty, isOpen]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div 
        className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            {post ? 'Edit Resource' : 'Create New Resource'}
          </h2>
          <div className="flex items-center space-x-3">
            {isDirty && (
              <div className="flex items-center text-amber-600">
                <AlertCircle className="h-4 w-4 mr-1" />
                <span className="text-sm">Unsaved changes</span>
              </div>
            )}
            {onPreview && (
              <button
                type="button"
                onClick={createSafeClickHandler(onPreview)}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors inline-flex items-center"
              >
                <Eye className="h-4 w-4 mr-2" />
                Preview
              </button>
            )}
            <button
              type="button"
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600 p-1"
            >
              <X className="h-5 w-5" />
            </button>
          </div>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="flex-1 overflow-y-auto">
          <div className="p-6 space-y-6">
            {/* Title */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Title *
              </label>
              <input
                type="text"
                value={formData.title}
                onChange={(e) => handleFieldChange('title', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter resource title..."
                required
              />
            </div>

            {/* Category and Status */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Category
                </label>
                <select
                  value={formData.category}
                  onChange={(e) => handleFieldChange('category', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  {CATEGORIES.map(category => (
                    <option key={category} value={category}>{category}</option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Status
                </label>
                <select
                  value={formData.status}
                  onChange={(e) => handleFieldChange('status', e.target.value as 'draft' | 'published')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="draft">Draft</option>
                  <option value="published">Published</option>
                </select>
              </div>
            </div>

            {/* Excerpt */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Excerpt
              </label>
              <textarea
                value={formData.excerpt}
                onChange={(e) => handleFieldChange('excerpt', e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Brief description of the resource..."
              />
            </div>

            {/* Featured Image */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Featured Image
              </label>
              <div className="space-y-4">
                <div className="flex space-x-4">
                  <button
                    type="button"
                    onClick={() => setImageUploadType('url')}
                    className={`px-4 py-2 rounded-lg border transition-colors ${
                      imageUploadType === 'url'
                        ? 'bg-blue-50 border-blue-200 text-blue-700'
                        : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    <Link className="h-4 w-4 mr-2 inline" />
                    Image URL
                  </button>
                  <button
                    type="button"
                    onClick={() => setImageUploadType('upload')}
                    className={`px-4 py-2 rounded-lg border transition-colors ${
                      imageUploadType === 'upload'
                        ? 'bg-blue-50 border-blue-200 text-blue-700'
                        : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    <Upload className="h-4 w-4 mr-2 inline" />
                    Upload File
                  </button>
                </div>

                {imageUploadType === 'url' ? (
                  <input
                    type="url"
                    value={formData.image}
                    onChange={(e) => {
                      handleFieldChange('image', e.target.value);
                      setImagePreview(e.target.value);
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="https://example.com/image.jpg"
                  />
                ) : (
                  <input
                    type="file"
                    accept="image/*"
                    onChange={(e) => {
                      const file = e.target.files?.[0];
                      if (file) handleImageUpload(file);
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                )}

                {imagePreview && (
                  <div className="mt-4">
                    <img
                      src={imagePreview}
                      alt="Preview"
                      className="max-w-xs h-auto rounded-lg border border-gray-200"
                    />
                  </div>
                )}
              </div>
            </div>

            {/* Tags and Author */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tags
                </label>
                <input
                  type="text"
                  value={formData.tags}
                  onChange={(e) => handleFieldChange('tags', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="tag1, tag2, tag3..."
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Author
                </label>
                <input
                  type="text"
                  value={formData.author}
                  onChange={(e) => handleFieldChange('author', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>

            {/* SEO Section */}
            <div className="border-t border-gray-200 pt-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">SEO Settings</h3>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    SEO Title
                  </label>
                  <input
                    type="text"
                    value={formData.seoTitle}
                    onChange={(e) => handleFieldChange('seoTitle', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="SEO optimized title..."
                    maxLength={60}
                  />
                  <div className="flex justify-between mt-1">
                    <span className="text-xs text-gray-500">Recommended: 50-60 characters</span>
                    <span className={`text-xs ${formData.seoTitle.length > 60 ? 'text-red-500' : 'text-gray-500'}`}>
                      {formData.seoTitle.length}/60
                    </span>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    SEO Description
                  </label>
                  <textarea
                    value={formData.seoDescription}
                    onChange={(e) => handleFieldChange('seoDescription', e.target.value)}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Brief description for search engines..."
                    maxLength={160}
                  />
                  <div className="flex justify-between mt-1">
                    <span className="text-xs text-gray-500">Recommended: 150-160 characters</span>
                    <span className={`text-xs ${formData.seoDescription.length > 160 ? 'text-red-500' : 'text-gray-500'}`}>
                      {formData.seoDescription.length}/160
                    </span>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    URL Slug
                  </label>
                  <input
                    type="text"
                    value={formData.slug}
                    onChange={(e) => handleFieldChange('slug', e.target.value.toLowerCase().replace(/[^a-z0-9-]/g, '-').replace(/-+/g, '-'))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="url-friendly-slug"
                  />
                  {formData.slug && (
                    <div className="mt-2 p-2 bg-blue-50 rounded border border-blue-200">
                      <span className="text-sm text-blue-700">
                        <strong>Preview URL:</strong> /resources/{formData.slug}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Content Editor */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Content *
              </label>
              <WordPressEditor
                value={formData.content}
                onChange={(content) => handleFieldChange('content', content)}
                placeholder="Write your resource content here..."
                height="400px"
                disabled={saving}
              />
            </div>
          </div>

          {/* Footer */}
          <div className="flex justify-end space-x-4 p-6 border-t border-gray-200 bg-gray-50">
            <button
              type="button"
              onClick={handleClose}
              className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={saving || !formData.title.trim() || !formData.content.trim()}
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 inline-flex items-center"
            >
              {saving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Saving...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  {post ? 'Update' : 'Create'} Resource
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ResourcePostForm;
