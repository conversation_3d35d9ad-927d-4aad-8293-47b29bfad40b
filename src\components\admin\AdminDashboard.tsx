import React, { useState } from 'react';
import { Routes, Route } from 'react-router-dom';
import AdminSidebar from './components/AdminSidebar';
import AdminHeader from './components/AdminHeader';
import DashboardOverview from './components/DashboardOverview';
import OrdersManagement from './components/OrdersManagement';
import CustomersManagement from './components/CustomersManagement';
import ContentManagement from './components/ContentManagement';
import Analytics from './components/Analytics';
import PodcastManagement from './components/PodcastManagement';
import ResourceManagement from './components/ResourceManagement';
import EbookManagement from './components/EbookManagement';

const AdminDashboard: React.FC = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Sidebar */}
      <AdminSidebar isOpen={sidebarOpen} onClose={() => setSidebarOpen(false)} />

      {/* Main content */}
      <div className="flex-1 flex flex-col lg:ml-64 min-w-0">
        {/* Header */}
        <AdminHeader onMenuClick={() => setSidebarOpen(true)} />

        {/* Page content */}
        <main className="flex-1 py-4 sm:py-6">
          <div className="max-w-full mx-auto px-2 sm:px-4 lg:px-6 xl:px-8">
            <Routes>
              <Route path="/" element={<DashboardOverview />} />
              <Route path="/orders" element={<OrdersManagement />} />
              <Route path="/customers" element={<CustomersManagement />} />
              <Route path="/content" element={<ContentManagement />} />
              <Route path="/resources" element={<ResourceManagement />} />
              <Route path="/ebook" element={<EbookManagement />} />
              <Route path="/podcast" element={<PodcastManagement />} />
              <Route path="/analytics" element={<Analytics />} />
            </Routes>
          </div>
        </main>
      </div>
    </div>
  );
};

export default AdminDashboard;
